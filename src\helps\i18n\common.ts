import { YoN } from '@/consts/y-o-n';
import { namespaceT } from '@/helps/namespace-t';

type MapKeyType = keyof YoN | boolean | number | string;
export function getYoNI18nText(value) {
  const t = namespaceT('consts.yon');
  const mapper = new Map<MapKeyType, string>([
    [true, t('y')],
    [1, t('y')],
    [YoN.Y, t('y')],
    [false, t('n')],
    [0, t('n')],
    [YoN.N, t('n')],
  ]);

  return mapper.get(value) || value;
}

export function getStatusI18nText(value) {
  const t = namespaceT('consts.status');
  const mapper = new Map<MapKeyType, string>([
    [true, t('y')],
    [1, t('y')],
    [YoN.Y, t('y')],
    [false, t('n')],
    [0, t('n')],
    [YoN.N, t('n')],
  ]);

  return mapper.get(value) || value;
}
