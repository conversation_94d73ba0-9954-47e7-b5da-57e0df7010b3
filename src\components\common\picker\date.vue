<script setup lang="ts">
interface DisabledDateFunc {
  (date: Date): boolean;
}

interface Props {
  disabledDate?: DisabledDateFunc;
  placeholder?: string | undefined;
  clearable?: boolean;
  format?: string | undefined;
}

withDefaults(defineProps<Props>(), {
  disabledDate() {
    return false;
  },
  placeholder: '',
  clearable: true,
  format: 'yyyy/MM/dd',
});
</script>


<template>
  <DatePicker
    class="pima-date-picker"
    v-bind="$attrs"
    :clearable="clearable"
    :format="format"
    :options="{ disabledDate }"
    :placeholder="placeholder || $t('common.placeholder.yearMonthDay')"
  />
</template>
