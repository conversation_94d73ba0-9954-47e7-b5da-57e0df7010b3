<script lang='ts' setup>
import { getCurrentInstance, ref, computed, onMounted,defineAsyncComponent } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import ClientOnly from '@/components/common/client-only';

import { useSider } from '@/uses/sider';
import emitter from '@/utils/event-bus';
import { isCurrentRoot, isCurrentForbidden, goForbidden } from '@/helps/navigation';


// // eslint-disable-next-line import/no-unresolved
const PimaAppSider = defineAsyncComponent(() => import('pimaRemoteUI/PimaAppSider'));

interface EmitType {
  (e: 'rebuild'): void
}
const emit = defineEmits<EmitType>();

const vm = getCurrentInstance();
const route = useRoute();
const router = useRouter();
const sider = useSider();
const show = ref(false);

/**
 * NOTE
 * 包含需要显示选中状态菜单的所有code
 * 建议定义在路由结构的meta.activeCodes里，也可以自己实现获取activeCodes的方法
 */
const activeCodes = computed(() => {
  const { activeCodes: codes } = route.meta ?? {};
  return Array.isArray(codes) ? codes : [];
});

const openCodes = computed(() => {
  const { openCodes: codes } = route.meta ?? {};
  return Array.isArray(codes) ? codes : [];
});

onMounted(() => {
  show.value = true;
});

function onSelect({ url, target }) {
  const resolved = router.resolve(url);

  if (!resolved.name) {
    window.open(url, target);
    return;
  }

  if (route.name === resolved.name) {
    emit('rebuild');
    return;
  }

  router.push(resolved);
}

function goFirstRoute() {
  const firstRoute = sider.getFirstRoute();

  if (firstRoute) {
    router.replace({ name: firstRoute.routeName });
  }
}

function bindEvents() {
  // 公共头部聚合了初始数据，在这里监听数据获取情况
  emitter.on('userDataFetched', ({ error, sider: siderData }) => {
    if (error?.code === 'UNAUTHORIZED' || !Array.isArray(siderData)) {
      vm.proxy.$setAuths([]);

      if (!isCurrentForbidden(router)) {
        goForbidden(router);
      }

      return;
    }

    sider.setMenu(siderData);
    if (siderData.length === 0) {
      sider.shutSider();
      if (!isCurrentForbidden(router)) {
        goForbidden(router);
      }

      return;
    }

    sider.openSider();

    if (isCurrentRoot(router)
    || isCurrentForbidden(router)
    || !sider.hasMenuByActiveCodes(activeCodes.value)) {
      goFirstRoute();
    }
  });

  // 公共头部点击应用名称事件，在这里监听并处理
  emitter.on('clickServiceName', () => {
    goFirstRoute();
  });
}

bindEvents();
</script>


<template>
  <ClientOnly>
    <PimaAppSider
      :value="sider.menu.value"
      :selected-keys="activeCodes"
      @select="onSelect"
    />
  </ClientOnly>
</template>
