<script lang="ts" setup>
import { ref } from 'vue';

import PimaModal from '@/components/common/pima-modal.vue';


withDefaults(defineProps<{
  title:string,
  content:string
}>(), {
  title: '',
  content: '',
});

const emit = defineEmits<{
  'on-confirm': [],
  'on-cancel': [],
}>();


const visible = ref(false);
const loading = ref(false);

const onCancel = () => {
  visible.value = false;
  emit('on-cancel');
};


defineExpose({
  onOpen: () => { visible.value = true; },
  onClose: () => { visible.value = false; },
});


</script>


<template>
  <PimaModal
    v-model="visible"
    :title="title"
    :width="480"
    :loading="loading"
    v-bind="$attrs"
    @cancel="onCancel"
    @confirm=" emit('on-confirm');"
  >
    <div class="content">
      <Icon
        type="ios-alert-outline"
        class="icon-warnning"
      />
      <div class="description">
        <slot>{{ content }}</slot>
      </div>
    </div>
  </PimaModal>
</template>


<style lang="less" scoped>
.content {
  display: flex;
  align-items: center;
  justify-content: center;

  .icon-warnning {
    width: 44px;
    color: var(--primary-color);
    font-size: 44px;
  }

  .description {
    margin-left: 15px;
    color: fade(#000, 85%);
    font-weight: normal;
    font-size: 16px;
  }


}
</style>
