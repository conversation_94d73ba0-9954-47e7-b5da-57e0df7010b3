<script setup lang="ts">

interface Props {
  
}

const props = withDefaults(defineProps<Props>(), {
  
});


</script>


<template>
   <Button
          class="pima-btn"
          type="primary"
          @click="onAdd"
        >
          {{ t('action.addRequirement') }}
        </Button>

         <Button
          class="pima-btn"
          type="primary"
          @click="onAdd"
        >
          {{ t('action.addRequirement') }}
        </Button>
</template>


<style lang="less" scoped>

</style>