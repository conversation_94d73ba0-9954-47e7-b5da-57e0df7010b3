<script setup lang="ts">
import TitleBar from '@/components/common/title-bar.vue';

import { namespaceT } from '@/helps/namespace-t';
import { useSider } from '@/uses/sider';
import { SMC } from '@/config/sider-menu';

interface Props {

}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const props = withDefaults(defineProps<Props>(), {

});

defineOptions({
  name: 'ClassMgt',
});

const { getMenuName } = useSider();


</script>


<template>
  <TitleBar :title="getMenuName((SMC.ClassManagement)=>SMC.ClassManagement)" />
</template>
