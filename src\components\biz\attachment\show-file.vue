<script setup lang="ts">
import { AttachmentVO } from '^/types/attachment';


interface Props {
  attachments: AttachmentVO[]
  size?:number
  multiple?:boolean
}

withDefaults(defineProps<Props>(), {
  attachments: () => [],
  size: 100,
  multiple: false,
});


</script>


<template>
  <template v-if="attachments && attachments.length > 0">
    <div
      v-if="!multiple"
      class="single-image"
    >
      <a
        :href="attachments[0].filePath.normal"
        target="_blank"
      >
        {{ attachments[0].fileName }}
      </a>
    </div>

    <div
      v-else
      class="multiple-images"
    >
      <a
        v-for="attachment in attachments"
        :key="attachment.id"
        :href="attachment.filePath.normal"
        target="_blank"
      >
        {{ attachment.fileName }}
      </a>
    </div>
  </template>
</template>


<style lang="less" scoped>
.multiple-images{
  display: flex;
  flex-wrap: wrap;
  gap:10px;
  align-items: center;

}
</style>
