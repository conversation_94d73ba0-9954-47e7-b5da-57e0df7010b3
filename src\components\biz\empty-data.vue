<script setup lang="ts">

interface Props {
  text:string
}

withDefaults(defineProps<Props>(), {
  text: '',
});


</script>


<template>
  <div class="empty-data">
    <img
      src="@/assets/img/empty-data.png"
      alt=""
    >
    <span class="text">
      {{ text }}
    </span>
  </div>
</template>


<style lang="less" scoped>
.empty-data{
  display: flex;
  flex-direction: column;
  gap: 3vw;
  align-items: center;
  justify-content: center;

  .text{
    color:#999;
    font-size: 15px;
  }
}
</style>
